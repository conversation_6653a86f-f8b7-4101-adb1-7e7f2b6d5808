<template>
  <div
    v-if="shouldShowTicker"
    :class="[
      'news-ticker-container',
      { 'minimized': isMinimized, 'expanded': !isMinimized }
    ]"
    ref="tickerContainer"
  >
    <!-- Toggle Button -->
    <button
      @click="toggleMinimized"
      class="ticker-toggle-btn"
      :aria-label="isMinimized ? 'Expand news ticker' : 'Minimize news ticker'"
    >
      <q-icon
        :name="isMinimized ? 'expand_more' : 'expand_less'"
        size="20px"
      />
    </button>

    <!-- News Content -->
    <div v-if="!isMinimized" class="ticker-content">
      <div class="ticker-header">
        <q-icon name="campaign" size="20px" class="news-icon" />
        <span class="ticker-title">Latest News</span>
      </div>

      <div class="ticker-scroll-container" ref="scrollContainer">
        <div
          class="ticker-scroll-content"
          :style="{ animationDuration: `${scrollDuration}s` }"
          @animationend="onAnimationEnd"
        >
          <div
            v-for="(item, index) in activeNewsItems"
            :key="item.id"
            class="news-item"
            @click="onNewsItemClick(item)"
          >
            <span class="news-category" :class="`category-${item.category}`">
              {{ item.category.toUpperCase() }}
            </span>
            <span class="news-title">{{ item.title }}</span>
            <span class="news-separator">•</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Minimized View -->
    <div v-else class="ticker-minimized">
      <q-icon name="campaign" size="16px" class="news-icon-mini" />
      <span class="ticker-mini-text">{{ activeNewsItems.length }} news updates</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { supabase } from '../../lib/supabase';

// Types
interface NewsItem {
  id: string;
  title: string;
  content: string;
  category: string;
  priority: number;
  is_active: boolean;
  start_date: string;
  end_date?: string;
  created_at: string;
}

// Composables
const $q = useQuasar();

// Reactive state
const newsItems = ref<NewsItem[]>([]);
const isMinimized = ref(false);
const shouldShowTicker = ref(true);
const tickerContainer = ref<HTMLElement>();
const scrollContainer = ref<HTMLElement>();
const scrollDuration = ref(30);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Computed
const activeNewsItems = computed(() => {
  const now = new Date();
  return newsItems.value
    .filter(item => {
      if (!item.is_active) return false;

      const startDate = new Date(item.start_date);
      if (startDate > now) return false;

      if (item.end_date) {
        const endDate = new Date(item.end_date);
        if (endDate < now) return false;
      }

      return true;
    })
    .sort((a, b) => b.priority - a.priority || new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
});

// Local storage key for user preferences
const STORAGE_KEY = 'smilefactory_news_ticker_minimized';

// Methods
const loadUserPreferences = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved !== null) {
      isMinimized.value = JSON.parse(saved);
    }
  } catch (error) {
    console.warn('Failed to load news ticker preferences:', error);
  }
};

const saveUserPreferences = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(isMinimized.value));
  } catch (error) {
    console.warn('Failed to save news ticker preferences:', error);
  }
};

const toggleMinimized = () => {
  isMinimized.value = !isMinimized.value;
  userHasManuallyToggled = true; // Mark that user has manually interacted
  saveUserPreferences();
};

const fetchNewsItems = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const { data, error: fetchError } = await supabase
      .from('news_items')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false });

    if (fetchError) {
      throw fetchError;
    }

    newsItems.value = data || [];
  } catch (err: any) {
    console.error('Error fetching news items:', err);
    error.value = err.message || 'Failed to load news';
    $q.notify({
      type: 'negative',
      message: 'Failed to load news updates',
      position: 'top'
    });
  } finally {
    isLoading.value = false;
  }
};

const onNewsItemClick = (item: NewsItem) => {
  $q.dialog({
    title: item.title,
    message: item.content,
    html: true,
    ok: 'Close'
  });
};

const onAnimationEnd = () => {
  // Restart animation for continuous scrolling
  if (scrollContainer.value) {
    const scrollContent = scrollContainer.value.querySelector('.ticker-scroll-content') as HTMLElement;
    if (scrollContent) {
      scrollContent.style.animation = 'none';
      scrollContent.offsetHeight; // Trigger reflow
      scrollContent.style.animation = '';
    }
  }
};

// Calculate scroll duration based on content length
const updateScrollDuration = () => {
  if (activeNewsItems.value.length > 0) {
    const baseSpeed = 50; // pixels per second
    const contentLength = activeNewsItems.value.reduce((total, item) =>
      total + item.title.length + item.category.length + 20, 0
    );
    scrollDuration.value = Math.max(15, contentLength / baseSpeed);
  }
};

// Enhanced scroll detection for auto-minimize
let intersectionObserver: IntersectionObserver | null = null;
let lastScrollY = 0;
let scrollTimeout: NodeJS.Timeout | null = null;
let userHasManuallyToggled = false;

const setupScrollObserver = () => {
  if (!tickerContainer.value) return;

  // Check if user has manually toggled the ticker
  const savedPreference = localStorage.getItem(STORAGE_KEY);
  userHasManuallyToggled = savedPreference !== null;

  // Intersection Observer for visibility detection
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Ticker is visible - restore to user preference or default expanded
          if (!userHasManuallyToggled) {
            isMinimized.value = false;
          }
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }
  );

  intersectionObserver.observe(tickerContainer.value);

  // Enhanced scroll direction detection
  const handleScroll = () => {
    const currentScrollY = window.scrollY;
    const scrollDirection = currentScrollY > lastScrollY ? 'down' : 'up';

    // Clear existing timeout
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // Only auto-minimize if user hasn't manually set preference
    if (!userHasManuallyToggled) {
      if (scrollDirection === 'down' && currentScrollY > 100) {
        // User is scrolling down and has scrolled past header area
        isMinimized.value = true;
      } else if (scrollDirection === 'up' && currentScrollY < 50) {
        // User is scrolling up and near the top
        isMinimized.value = false;
      }
    }

    // Set timeout to reset scroll detection after user stops scrolling
    scrollTimeout = setTimeout(() => {
      // Optional: Add any post-scroll behavior here
    }, 150);

    lastScrollY = currentScrollY;
  };

  // Add scroll listener with throttling
  let ticking = false;
  const throttledScrollHandler = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        handleScroll();
        ticking = false;
      });
      ticking = true;
    }
  };

  window.addEventListener('scroll', throttledScrollHandler, { passive: true });

  // Store the cleanup function
  const cleanup = () => {
    window.removeEventListener('scroll', throttledScrollHandler);
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
  };

  // Return cleanup function for use in onUnmounted
  return cleanup;
};

// Realtime subscription
let realtimeSubscription: any = null;

const setupRealtimeSubscription = () => {
  realtimeSubscription = supabase
    .channel('news_items_changes')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'news_items'
      },
      (payload) => {
        console.log('News items changed:', payload);
        fetchNewsItems(); // Refresh news items
      }
    )
    .subscribe();
};

// Lifecycle
let scrollCleanup: (() => void) | null = null;

onMounted(async () => {
  loadUserPreferences();
  await fetchNewsItems();
  scrollCleanup = setupScrollObserver();
  setupRealtimeSubscription();
});

onUnmounted(() => {
  if (intersectionObserver) {
    intersectionObserver.disconnect();
  }
  if (realtimeSubscription) {
    realtimeSubscription.unsubscribe();
  }
  if (scrollCleanup) {
    scrollCleanup();
  }
});

// Watchers
watch(activeNewsItems, updateScrollDuration, { immediate: true });
</script>

<style scoped>
.news-ticker-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(13, 138, 62, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-family: 'Rubik', sans-serif;
}

.news-ticker-container.expanded {
  height: auto;
  min-height: 50px;
}

.news-ticker-container.minimized {
  height: 35px;
}

.ticker-toggle-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: background-color 0.2s ease;
  z-index: 1001;
}

.ticker-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.ticker-content {
  display: flex;
  align-items: center;
  padding: 10px 50px 10px 15px;
  min-height: 50px;
}

.ticker-header {
  display: flex;
  align-items: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.news-icon {
  color: white;
  margin-right: 8px;
}

.ticker-title {
  color: white;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
}

.ticker-scroll-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 30px;
}

.ticker-scroll-content {
  display: flex;
  align-items: center;
  animation: scroll-left linear infinite;
  white-space: nowrap;
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.news-item {
  display: inline-flex;
  align-items: center;
  margin-right: 40px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.news-item:hover {
  opacity: 0.8;
}

.news-category {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  margin-right: 10px;
  text-transform: uppercase;
}

.category-announcement {
  background: rgba(245, 166, 35, 0.8);
}

.category-event {
  background: rgba(156, 39, 176, 0.8);
}

.category-partnership {
  background: rgba(33, 150, 243, 0.8);
}

.news-title {
  color: white;
  font-size: 14px;
  font-weight: 400;
}

.news-separator {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 15px;
  font-weight: bold;
}

.ticker-minimized {
  display: flex;
  align-items: center;
  padding: 8px 50px 8px 15px;
  height: 35px;
}

.news-icon-mini {
  color: white;
  margin-right: 8px;
}

.ticker-mini-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticker-content {
    padding: 8px 40px 8px 10px;
  }

  .ticker-header {
    margin-right: 15px;
  }

  .ticker-title {
    font-size: 12px;
  }

  .news-title {
    font-size: 12px;
  }

  .news-category {
    font-size: 9px;
    padding: 1px 6px;
  }
}

@media (max-width: 480px) {
  .ticker-header .ticker-title {
    display: none;
  }

  .ticker-content {
    padding: 8px 35px 8px 35px;
  }

  .news-item {
    margin-right: 25px;
  }
}
</style>