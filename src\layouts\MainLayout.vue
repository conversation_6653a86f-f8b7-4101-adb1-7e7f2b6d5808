<template>
  <q-layout view="hHh lpR lFf">
    <!-- News Ticker Component -->
    <NewsTickerComponent />

    <!-- Mobile Navigation Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      bordered
      :width="250"
      class="bg-blacki text-white mobile-nav-drawer"
      side="left"
      behavior="mobile"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <q-item-label header class="text-primary text-weight-bold">Navigation</q-item-label>

          <!-- Home Link -->
          <q-item clickable v-ripple to="/" exact active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="home" color="primary" />
            </q-item-section>
            <q-item-section>Home</q-item-section>
          </q-item>

          <!-- About Link -->
          <q-item clickable v-ripple to="/about" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="info" color="primary" />
            </q-item-section>
            <q-item-section>About</q-item-section>
          </q-item>

          <!-- Virtual Community Link -->
          <q-item clickable v-ripple @click="navigateTo('/virtual-community?tab=feed')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="people" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Virtual Community</q-item-label>
              <q-item-label caption>Explore our online community</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-badge color="accent" text-color="white" rounded>NEW</q-badge>
            </q-item-section>
          </q-item>

          <!-- Hub Facilities Link -->
          <q-item clickable v-ripple @click="navigateTo('/hub-facilities')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="business" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Hub Facilities</q-item-label>
              <q-item-label caption>Explore our physical spaces</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Contact Us Link -->
          <q-item clickable v-ripple to="/contact-us" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="contact_support" color="primary" />
            </q-item-section>
            <q-item-section>Contact Us</q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Header -->
    <q-header class="bg-menu q-py-sm shadow-1">
      <q-toolbar>
        <!-- Hamburger menu for mobile -->
        <q-btn
          flat
          dense
          round
          color="primary"

          icon="menu"
          aria-label="Menu"
          class="lt-xs hamburger-btn q-mr-xs q-pa-xs"
          @click="leftDrawerOpen = !leftDrawerOpen"
        />

        <div class="container q-mx-auto q-px-md">
          <div class="row">
            <div class="col-1" />
            <div class="col-10">
              <div class="row full-width items-center">
                <!-- Social Media Icons -->
                <div class="col-3 social-icons gt-sm">
                  <a :href="socialLinks.twitter" target="_blank" class="social-link">
                    <span class="icon icon-twitter"></span>
                  </a>
                  <a :href="socialLinks.instagram" target="_blank" class="social-link">
                    <span class="icon icon-instagram"></span>
                  </a>
                  <a :href="socialLinks.linkedin" target="_blank" class="social-link">
                    <span class="icon icon-linkedin"></span>
                  </a>
                </div>

                <!-- Logo and Title - Centered -->
                <div class="col-6 flex justify-center items-center">
                  <router-link to="/" class="logo-link">
                    <div class="logo-container flex items-center">
                      <div class="logo-img">
                        <img :src="logoUrl" alt="ZbInnovation Logo" class="logo-image" style="height: 50px;">
                      </div>
                      <div class="q-ml-sm text-weight-bold logo-text">ZbInnovation</div>
                    </div>
                  </router-link>
                </div>

                <!-- Conditional Buttons based on auth status -->
                <div class="col-sm-3 col-md-3 col-lg-3  col-xl-3 col-xs-5 flex justify-end">
                  <!-- Authentication-based content -->
                  <template v-if="!isAuthenticated">
                    <!-- Show button group when not authenticated -->
                    <q-btn-group spread rounded class="auth-btn-group">
                      <q-btn
                        color="primary"
                          no-caps
                        @click="showSignupDialog = true"

                        style="background-color: #0D8A3E; color:
                        white; border: none;"

                      >
                       <div class="text-caption">
                        Sign Up
                       </div>


                      </q-btn>
                      <q-btn

                        @click="goToSignIn"
                        no-caps
                        style="background-color: #a4ca39; color: white; border: none;"

                      >
                      <div class="text-caption">
                        Sign In
                      </div>
                      </q-btn>
                    </q-btn-group>
                  </template>

                  <template v-else>
                    <!-- Show dashboard button when authenticated -->
                    <q-btn
                      color="green-9"
                      label="Dashboard"
                      :to="{ name: 'dashboard' }"
                      class="q-px-sm dashboard-btn"
                      outline
                      round
                      size="sm"
                    >
                      <template v-slot:prepend>
                        <unified-icon name="dashboard" class="q-mr-xs" />
                      </template>
                    </q-btn>
                  </template>

                  <!-- Sign Up Dialog -->
                  <q-dialog v-model="showSignupDialog" persistent>
                    <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
                      <q-card-section class="row items-center q-pb-none">
                        <div class="text-h6">Join Our Early Access Program</div>
                        <q-space />
                        <q-btn flat round dense v-close-popup>
                          <unified-icon name="close" />
                        </q-btn>
                      </q-card-section>

                      <q-card-section>
                        <auth-options
                          mode="signup"
                          @email-password-signup="handleEmailPasswordSignup"
                        />
                      </q-card-section>
                    </q-card>
                  </q-dialog>

                  <!-- Email Password Form Dialog -->
                  <q-dialog v-model="showEmailPasswordForm" persistent>
                    <q-card class="auth-dialog" style="min-width: 350px">
                      <q-card-section class="row items-center q-pb-none">
                        <div class="text-h6">Sign Up with Email</div>
                        <q-space />
                        <q-btn flat round dense v-close-popup>
                          <unified-icon name="close" />
                        </q-btn>
                      </q-card-section>

                      <q-card-section class="q-gutter-md">
                        <q-input
                          v-model="form.email"
                          type="email"
                          label="Email"
                          outlined
                          :rules="emailRules"
                        >
                          <template v-slot:prepend>
                            <unified-icon name="mail" />
                          </template>
                        </q-input>

                        <q-input
                          v-model="form.password"
                          :type="isPwd ? 'password' : 'text'"
                          label="Password"
                          outlined
                          :rules="passwordRules"
                        >
                          <template v-slot:prepend>
                            <unified-icon name="lock" />
                          </template>
                          <template v-slot:append>
                            <div class="cursor-pointer" @click="isPwd = !isPwd">
                              <unified-icon :name="isPwd ? 'eye-off' : 'eye'" />
                            </div>
                          </template>
                        </q-input>
                      </q-card-section>

                      <q-card-actions align="right">
                        <q-btn flat label="Cancel" color="primary" v-close-popup :disable="loading" />
                        <q-btn
                          flat
                          label="Submit"
                          color="primary"
                          @click="submitEmailPasswordSignup"
                          :loading="loading"
                          :disable="loading"
                        />
                      </q-card-actions>
                    </q-card>
                  </q-dialog>
                </div>
              </div>
            </div>
            <div class="col-1" />
          </div>
        </div>
      </q-toolbar>

      <!-- Navigation Bar - Flat with matching bg-menu color (hidden on mobile) -->
      <q-bar class="bg-menu text-primary flat gt-sm">
        <div class="container q-mx-auto q-px-md">
          <div class="row justify-center full-width">
            <!-- Home Link -->
            <div class="col-auto q-mx-sm">
              <q-btn flat no-caps label="Home" to="/" class="nav-btn" :class="{ 'router-link-active': isHomeActive }" />
            </div>

            <!-- About Link -->
            <div class="col-auto q-mx-sm">
              <q-btn flat no-caps label="About" to="/about" class="nav-btn" :class="{ 'router-link-active': isAboutActive }" />
            </div>

            <!-- Virtual Community Button -->
            <div class="col-auto q-mx-sm">
              <q-btn
                flat
                no-caps
                to="/virtual-community?tab=feed"
                class="nav-btn"
                :class="{ 'router-link-active': isVirtualCommunityActive }"
              >
                <div class="row items-center no-wrap">
                  <div>Virtual Community</div>
                  <q-badge color="accent" text-color="white" class="q-ml-sm badge-new" rounded>NEW</q-badge>
                </div>
              </q-btn>
            </div>

            <!-- Hub Facilities Button -->
            <div class="col-auto q-mx-sm">
              <q-btn
                flat
                no-caps
                label="Hub Facilities"
                to="/hub-facilities"
                class="nav-btn"
                :class="{ 'router-link-active': isHubFacilitiesActive }"
              />
            </div>

            <!-- Contact Us Link -->
            <div class="col-auto q-mx-sm">
              <q-btn flat no-caps label="Contact Us" to="/contact-us" class="nav-btn" :class="{ 'router-link-active': isContactActive }" />
            </div>
          </div>
        </div>
      </q-bar>
    </q-header>

    <!-- No sidebar for landing page -->

    <!-- Main Content -->
    <q-page-container>
      <router-view v-scroll-to-top></router-view>
    </q-page-container>

    <!-- Footer -->
    <TheFooter />
  </q-layout>
</template>

<script lang="ts">
export default {
  name: 'MainLayout'
};
</script>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useNotificationStore } from '../stores/notifications';
import { useMegaMenuStore } from '../stores/megaMenu';
// No need for Quasar loading import
import TheFooter from '../components/common/AppFooter.vue';
import { default as UnifiedIcon } from '../components/ui/UnifiedIcon.vue';
import { default as AuthOptions } from '../components/auth/AuthOptions.vue';
import NewsTickerComponent from '../components/news/NewsTickerComponent.vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const megaMenuStore = useMegaMenuStore();
const logoUrl = ref('/logo.png');

// Computed properties for active routes
const isHomeActive = computed(() => route.path === '/');
const isAboutActive = computed(() => route.path === '/about');
const isContactActive = computed(() => route.path === '/contact-us');
const isVirtualCommunityActive = computed(() => route.path.includes('/virtual-community'));
const isHubFacilitiesActive = computed(() => route.path.includes('/hub-facilities') || route.path.includes('/hub-events'));

// Drawer control
const leftDrawerOpen = ref(false);

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated);

// Dialog controls
const showSignupDialog = ref(false);
const showEmailPasswordForm = ref(false);
const loading = ref(false);
const isPwd = ref(true);

// Navigation function for drawer items
function navigateTo(path: string) {
  // Close the drawer
  leftDrawerOpen.value = false;

  // Close any open mega menus
  if (megaMenuStore.isAnyMenuOpen) {
    megaMenuStore.closeMenu();
  }

  // Use a small timeout to ensure the drawer is closed before navigation
  setTimeout(() => {
    // Use router.push with catch to handle any navigation errors
    router.push(path).catch(err => {
      console.error('Navigation error:', err);
      // If navigation fails, try again with name-based navigation
      if (typeof path === 'string' && (path.includes('/virtual-community') || path.includes('/hub-facilities'))) {
        const tabMatch = path.match(/\?tab=([^&]+)/);
        const tab = tabMatch ? tabMatch[1] : (path.includes('/virtual-community') ? 'feed' : undefined);
        router.push({
          path: path.split('?')[0],
          query: tab ? { tab } : {}
        }).catch(secondErr => {
          console.error('Second navigation attempt failed:', secondErr);
        });
      }
    });
  }, 50);
}



// Form data
const form = ref({
  email: '',
  password: ''
});

// Form validation rules
const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
];

const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 6 || 'Password must be at least 6 characters'
];

const socialLinks = {
  twitter: 'https://twitter.com/zb_foryou',
  instagram: 'https://instagram.com/zb_foryou',
  linkedin: 'https://www.linkedin.com/company/zbforyou'
};

// Handle email password signup button click
const handleEmailPasswordSignup = () => {
  showSignupDialog.value = false;
  showEmailPasswordForm.value = true;
};

// Submit email password signup form with improved error handling
const submitEmailPasswordSignup = async () => {
  try {
    loading.value = true;

    // Basic validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!form.value.email || !emailRegex.test(form.value.email)) {
      throw new Error('Please enter a valid email address');
    }

    if (form.value.password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }

    console.log('Starting signup process with simplified approach...');

    // Use auth store for signup with our improved implementation
    const result = await authStore.signUp({
      email: form.value.email,
      password: form.value.password
    });

    if (result && result.user) {
      console.log('Signup successful with user:', result.user.id);
      // Close the dialog
      showEmailPasswordForm.value = false;

      // Reset form
      form.value.email = '';
      form.value.password = '';

      // Success is handled by the auth store (notification and redirect)
    } else {
      console.warn('Signup returned no user, but did not throw an error');
      // Try to sign in directly as a fallback
      try {
        await authStore.signIn(form.value.email, form.value.password);
        notificationStore.success('Account created and signed in successfully!');
        showEmailPasswordForm.value = false;

        // Reset form
        form.value.email = '';
        form.value.password = '';
      } catch (signInError) {
        console.error('Fallback sign-in failed:', signInError);
        notificationStore.warning('Account may have been created. Please try signing in.');
        showEmailPasswordForm.value = false;
      }
    }
  } catch (error: any) {
    console.error('Email password signup error:', error);
    notificationStore.error(error.message || 'Registration failed. Please try again.');
  } finally {
    loading.value = false;
  }
};

// Legacy scroll function (commented out to avoid unused function warning)
// const scrollToSignup = async () => {
//   await layoutStore.scrollToSignup();
// };

// Navigate to sign in page
const goToSignIn = () => {
  router.push({ name: 'sign-in' });
};
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.bg-menu {
  background-color: #dfefe6;
}

/* Adjust layout for news ticker */
:deep(.q-page-container) {
  padding-top: 50px; /* Space for news ticker */
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-link {
  text-decoration: none;
  color: #0D8A3E;
  transition: opacity 0.3s ease;
}

.logo-link:hover {
  opacity: 0.9;
}

.social-icons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.social-link {
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(13, 138, 62, 0.1);
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: rgba(13, 138, 62, 0.2);
}

.social-link .icon {
  width: 20px;
  height: 20px;
}

.signup-btn, .signin-btn {
  color: white;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 8px 16px;
  border-radius: 0;
  flex: 1 1 50% !important;
  min-width: 0 !important;
  width: 50% !important;
  letter-spacing: 0.5px;
}

.dashboard-btn {
  color: #0D8A3E;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 8px 16px;
  border-radius: 24px;
  border: 1px solid #0D8A3E;
}

.signup-btn:hover, .signin-btn:hover {
  opacity: 0.9;
}

.dashboard-btn:hover {
  background-color: rgba(13, 138, 62, 0.1);
}

.q-btn-group {
  overflow: hidden;
  display: flex;
  width: 100%;
}

/* Dialog styles */
.q-dialog .auth-dialog {
  border-radius: 12px;
  max-width: 400px;
}

.q-dialog .q-card__section {
  padding: 20px;
}

.q-dialog .text-h6 {
  color: #0D8A3E;
  font-weight: 600;
}

/* Drawer styles */
.q-drawer {
  background-color: #f5f5f5;
}

.q-item.q-router-link-active {
  color: #0D8A3E;
  background: rgba(13, 138, 62, 0.1);
  font-weight: 600;
  border-left: 3px solid #0D8A3E;
}

.q-item {
  color: #424242;
}

.q-item:hover {
  color: #0D8A3E;
  background: rgba(13, 138, 62, 0.05);
}

.q-item-label {
  color: #666666;
  font-weight: 500;
}

@media (max-width: 599px) {
  .container {
    padding: 0 16px !important;
  }

  .col-1 {
    display: none;
  }

  .col-10 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .logo-text {
    font-size: 0.9rem;
  }

  .logo-image {
    height: 40px !important;
  }

  .col-6 {
    justify-content: flex-start !important;
    flex: 0 1 auto;
  }

  .col-3.flex.justify-end {
    margin-left: auto;
    flex: 0 0 auto;
  }

  .signup-btn, .signin-btn {
    font-size: 0.8rem;
    padding: 6px 12px !important;
    flex: 1 1 50% !important;
    width: 50% !important;
    min-width: 0 !important;
    max-width: 50% !important;
    height: auto !important;
    min-height: 36px !important;
    border-radius: 0 !important;
    margin: 0;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .auth-btn-group {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: 0 4px;
    border-radius: 20px !important;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .auth-btn-group .q-btn {
    flex: 1 1 50% !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 50% !important;
    box-shadow: none;
  }

  /* First button in group (Sign Up) */
  .auth-btn-group .q-btn:first-child {
    border-top-left-radius: 20px !important;
    border-bottom-left-radius: 20px !important;
  }

  /* Last button in group (Sign In) */
  .auth-btn-group .q-btn:last-child {
    border-top-right-radius: 20px !important;
    border-bottom-right-radius: 20px !important;
  }

  .dashboard-btn {
    font-size: 0.8rem;
    padding: 4px 12px;
  }

  /* Dialog styles for mobile */
  .q-dialog .q-card {
    width: 90%;
  }

  /* Hamburger menu button styles */
  .hamburger-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0D8A3E;
    z-index: 10;
  }

  /* Navigation bar responsive styles */
  .q-bar {
    height: auto !important;
    padding: 8px 0;
    box-shadow: none;
    border-top: 1px solid rgba(13, 138, 62, 0.1);
  }

  .q-bar .q-btn {
    font-size: 0.75rem;
    padding: 2px 4px;
    min-height: 28px;
    color: #0D8A3E;
  }

  .nav-link {
    margin: 0 1px;
  }
}

/* Mobile Navigation Drawer Styles */
.mobile-nav-drawer {
  background-color: white;
  box-shadow: 1px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav-drawer .q-item {
  min-height: 48px;
  border-radius: 8px;
  margin-bottom: 4px;
}

.mobile-nav-drawer .q-item:hover {
  background-color: rgba(13, 138, 62, 0.05);
}

.mobile-nav-drawer .q-item.q-router-link-active {
  background-color: rgba(13, 138, 62, 0.1);
  color: #0D8A3E;
  font-weight: 500;
}

.mobile-nav-drawer .q-expansion-item__content {
  padding-left: 8px;
}

.mobile-nav-drawer .q-item-label.header {
  padding: 16px 16px 8px 16px;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.mobile-nav-drawer .q-expansion-item.q-expansion-item--expanded {
  background-color: rgba(13, 138, 62, 0.03);
  border-radius: 8px;
}

.mobile-nav-drawer .q-icon {
  font-size: 20px;
}

/* Navigation Bar Styles */
.nav-link {
  text-decoration: none;
  color: #0D8A3E;
}

.q-bar {
  height: 50px;
  box-shadow: none; /* Make it flat */
  border-top: 1px solid rgba(13, 138, 62, 0.1); /* Subtle separator */
}

.q-bar .q-btn {
  font-weight: 500;
  font-size: 14px;
  color: #0D8A3E;
}

.q-bar .q-btn:hover {
  background-color: rgba(13, 138, 62, 0.1);
}

/* Navigation and Mega Menu Styles */
.nav-btn {
  position: relative;
}

.nav-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: #0D8A3E;
  transition: width 0.3s ease;
}

.nav-btn:hover::after {
  width: 80%;
}

/* Active menu indicator */
.nav-btn.active::after,
.nav-btn.router-link-active::after {
  width: 80%;
}

/* Active link styles */
.router-link-active {
  font-weight: 600;
  color: #0D8A3E !important;
}

/* Mega Menu Container */
.mega-menu-container {
  position: relative;
}

/* Mega Menu Dropdown */
.mega-menu-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  margin-top: 5px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  max-width: 600px;
  min-width: 500px;
}

@media (max-width: 600px) {
  .mega-menu-dropdown {
    min-width: 95vw;
    max-width: 95vw;
    left: 0;
    transform: none;
  }
}

.featured-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.featured-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.hours-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background-color: rgba(13, 138, 62, 0.05);
}

.hours-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Badge styles */
.badge-new {
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
